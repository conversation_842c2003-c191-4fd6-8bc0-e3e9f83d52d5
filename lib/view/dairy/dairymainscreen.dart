// ignore_for_file: prefer_const_constructors, avoid_unnecessary_containers

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
// import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/dashboard/dashboardscreen.dart';
// import 'package:silverleaf/view/dairy/DairySubPage.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'package:flutter/services.dart';
import 'dart:io';

import 'package:url_launcher/url_launcher.dart';

var dairypages = 0;

class DairyMainScreen extends StatefulWidget {
  const DairyMainScreen({super.key});

  @override
  State<DairyMainScreen> createState() => _DairyMainScreenState();
}

class _DairyMainScreenState extends State<DairyMainScreen> {
  var jsonData;

  late SharedPreferences prefs;
  final scrollcontroller = ScrollController();
  bool isTeacher = false;
  back() {
    print('back button clicked');
    // Get.to(MainBoard());
    Get.back();
  }

  List dairyCuttentUpdate = ["12 Aug, 2023 | Saturday"];
  List name = [""];
  List content = [];
  List dOS = [];
  List ubName = [];
  List userNames = [];
  List<DropdownMenuItem<String>> branch = [];
  Future<Map<String, List<Map<String, dynamic>>>> fetchdairyData() async {
    Map<String, List<Map<String, dynamic>>> _jsonData = {};
    final url;
    if (isTeacher == false) {
      print(userNames[6]);
      print(userNames[5]);

      print(userNames[14]);
      url =
          'https://silverleafms.in/silvar_leaf/api/dairy/view-student-diary/${userNames[6]}/${userNames[5]}/${userNames[14]}';
    } else {
      url =
          'https://silverleafms.in/silvar_leaf/api/dairy/view-student-diary/${userNames[7]}/${userNames[6]}/${userNames[14]}';
    }
    //print(url);
    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      print('diary record');
      print(responseBody['data']);
      final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );

      for (Map<String, dynamic> item in data) {
        String dateKey = item['created_at'].toString();
        if (!_jsonData.containsKey(dateKey)) {
          _jsonData[dateKey] = [];
        }
        _jsonData[dateKey]!.add(item);
      }

      return _jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          setState(() {
            isTeacher = false;
          });
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
        }
      });
    }
  }

  void dispose() {
    super.dispose();
  }

  void initState() {
    super.initState();

    getListString();
    scrollcontroller.addListener(() {
      if (scrollcontroller.position.maxScrollExtent ==
          scrollcontroller.offset) {
        this.fetchdairyData();
      }
    });

    branch.add(DropdownMenuItem(value: '0', child: Text('Select')));
  }

  @override
  Widget build(BuildContext context) {
    //  return WillPopScope(onWillPop: () => back(), child: mainDairy());
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: CustomizedAppBar(
            back: back,
            profile: () {},
            screenName: 'Dairy',
            screen_id: 2,
          ),
        ),
        body: mainDairy(),
      ),
    );
  }

  popup(context) {
    return showDialog(
      context: context,
      builder:
          (BuildContext context) => AlertDialog(
            title: const Text('AlertDialog Title'),
            content: const Text('AlertDialog description'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.pop(context, 'Cancel'),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, 'OK'),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  Widget mainDairy() {
    return Column(
      children: [
        const SizedBox(),
        Expanded(
          child: Container(
            child: FutureBuilder<Map<String, List<Map<String, dynamic>>>>(
              future: fetchdairyData(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: CircularProgressIndicator());
                } else {
                  Map<String, List<Map<String, dynamic>>> groupedData =
                      snapshot.data!;
                  return ListView.separated(
                    separatorBuilder: (context, index) {
                      return const Divider();
                    },
                    itemCount: snapshot.data!.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      DateTime dateTime;
                      if (snapshot.data!.length == 0) {
                        return Center(child: Text('No Data'));
                      } else {
                        String dateKey = groupedData.keys.elementAt(index);
                        List<Map<String, dynamic>> dataForDate =
                            groupedData[dateKey]!;
                        int dateIndex = index ~/ 2;
                        bool isLastDate = dateIndex == groupedData.length - 1;
                        if (dateKey.contains('AM') || dateKey.contains('PM')) {
                          // 12-hour format with AM/PM
                          DateFormat inputFormat = DateFormat(
                            'yyyy-MM-dd hh:mm a',
                          );
                          dateTime = inputFormat.parse(dateKey);
                        } else {
                          // 24-hour format without AM/PM
                          DateFormat inputFormat = DateFormat(
                            'yyyy-MM-dd HH:mm:ss',
                          );
                          dateTime = inputFormat.parse(dateKey);
                        }

                        // Format the date and time
                        String formattedDate = DateFormat(
                          'd MMM y | EEEE',
                        ).format(dateTime);
                        String formattedTime = DateFormat(
                          'hh:mm a',
                        ).format(dateTime);

                        // String formattedDate = dateKey;
                        print("date generated");

                        print(dateKey);
                        return Container(
                          color:
                              index == 0 || index % 2 == 0
                                  ? Color(0xffF4FFEE)
                                  : Colors.transparent,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(left: 16.0, top: 10),
                                child: Text(
                                  // '$dateKey',
                                  formattedDate + " | " + formattedTime,
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ),
                              SizedBox(height: 8.0),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children:
                                    dataForDate.map((item) {
                                      var dos = item['given_date'];

                                      DateTime postedDate = DateTime.parse(
                                        item['date'],
                                      );
                                      String postedDates = DateFormat(
                                        'd MMM y, EEEE',
                                      ).format(postedDate);
                                      // DateTime inputDate =
                                      //     DateTime.parse(item['created_at']);

                                      print('response map');

                                      print(item['url']);

                                      return GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            Get.to(
                                              DairySecondScreenDetails(
                                                user_id: item['id'],
                                                title: item['title'],
                                                description:
                                                    item['description'],
                                                posted_date: item['date'],
                                                ending_date: dos,
                                                teacher_name:
                                                    item['role'] == 2
                                                        ? item['staff_name']
                                                        : 'Admin',
                                                url:
                                                    item['url'] != null
                                                        ? item['url']
                                                        : '',
                                              ),
                                            );
                                          });
                                          // Get.to(const DairySubPage());
                                        },
                                        child: Container(
                                          child: Padding(
                                            padding: EdgeInsets.all(17.0.sp),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                  child: Row(
                                                    children: [
                                                      Text(
                                                        //   "Social Science",
                                                        item['subject_name'],
                                                        style: dairyTextStyle
                                                            .copyWith(
                                                              color:
                                                                  subappcolor,
                                                              fontSize: 18,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w700,
                                                            ),
                                                      ),
                                                      Expanded(
                                                        child: SizedBox(),
                                                      ),
                                                      Text(
                                                        item['role'] == 2
                                                            ? item['staff_name']
                                                            : 'Admin',
                                                        style: dairyTextStyle
                                                            .copyWith(
                                                              color:
                                                                  Colors.grey,
                                                            ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                SizedBox(height: 10),
                                                Text(
                                                  item['title'],
                                                  style: dairyTextStyle
                                                      .copyWith(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                ),
                                                SizedBox(
                                                  child: Row(
                                                    children: [
                                                      Text(
                                                        "DOS : ${postedDates}",
                                                        style: dairyTextStyle
                                                            .copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                            ),
                                                      ),
                                                      const Expanded(
                                                        child: SizedBox(),
                                                      ),
                                                      GestureDetector(
                                                        onTap:
                                                            () => {
                                                              Get.to(
                                                                MainPageChat(
                                                                  staff_id:
                                                                      item['staff_id']
                                                                          .toString(),
                                                                  student_id:
                                                                      userNames[0],
                                                                  Section_name:
                                                                      item['section_name'],
                                                                  class_name:
                                                                      item['class_name'],
                                                                  Profile:
                                                                      item['profile'],
                                                                  name:
                                                                      item['staff_name'],
                                                                  sender_id:
                                                                      userNames[0],
                                                                  message_count:
                                                                      1,
                                                                  class_id:
                                                                      userNames.last ==
                                                                              '1'
                                                                          ? userNames[6]
                                                                          : userNames[6],
                                                                  section_id:
                                                                      userNames.last ==
                                                                              '1'
                                                                          ? userNames[5]
                                                                          : userNames[5],
                                                                  branch_id:
                                                                      userNames.last ==
                                                                              '1'
                                                                          ? userNames[7]
                                                                          : userNames[7],
                                                                ),
                                                              ),
                                                            },
                                                        child: Container(
                                                          height: 9.0.hp,
                                                          width: 10.0.wp,
                                                          decoration:
                                                              const BoxDecoration(
                                                                shape:
                                                                    BoxShape
                                                                        .circle,
                                                              ),
                                                          child: Image.asset(
                                                            'images/Group 161.png',
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                              ),
                            ],
                          ),
                        );
                      }
                    },
                  );
                }
              },
            ),
          ),
        ),
      ],
    );
  }
}

class DairySecondScreenDetails extends StatefulWidget {
  final String title;
  final String description;
  final int user_id;
  final String posted_date;
  final String ending_date;
  final String teacher_name;

  final String url;
  DairySecondScreenDetails({
    super.key,
    required this.user_id,
    required this.title,
    required this.description,
    required this.posted_date,
    required this.ending_date,
    required this.teacher_name,
    required this.url,
  });

  @override
  State<DairySecondScreenDetails> createState() =>
      _DairySecondScreenDetailsState();
}

class _DairySecondScreenDetailsState extends State<DairySecondScreenDetails> {
  subback() {
    // Get.to(const MainBoard());
    Get.back();
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    DateTime parsedDate = DateTime.parse(widget.ending_date);
    String formattedDate = DateFormat('d MMM y | EEEE').format(parsedDate);

    DateTime postedDate = DateTime.parse(widget.posted_date);
    String postedDates = DateFormat('d MMM y, EEEE').format(postedDate);

    print(widget.posted_date);
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: CustomizedAppBar(
            back: () {
              subback();
            },
            profile: () {},
            screenName: "Dairy",
            screen_id: 2,
          ),
        ),
        body: WillPopScope(
          onWillPop: () => subback(),
          child: Column(
            children: [
              SizedBox(height: 2.0.hp),
              Expanded(
                child: Container(
                  child: Padding(
                    padding: EdgeInsets.all(17.0.sp),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          formattedDate,

                          // DateFormat.yMMMEd()
                          //     .format(DateTime.parse(widget.posted_date)),
                          style: dairyTextStyle.copyWith(
                            fontSize: 12,
                            fontWeight: FontWeight.w900,
                            color: Colors.black,
                          ),
                        ),
                        SizedBox(height: 10),
                        SizedBox(
                          child: Row(
                            children: [
                              Text(
                                widget.title,
                                style: dairyTextStyle.copyWith(
                                  color: subappcolor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Expanded(child: SizedBox()),
                              Text(
                                widget.teacher_name,
                                style: dairyTextStyle.copyWith(
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 10),
                        Text(
                          widget.description,
                          style: dairyTextStyle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 10),
                        SizedBox(
                          child: Row(
                            children: [
                              Text(
                                'DOS:${postedDates},',
                                style: dairyTextStyle.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const Expanded(child: SizedBox()),
                              // Container(
                              //   height: 9.0.hp,
                              //   width: 10.0.wp,
                              //   decoration: const BoxDecoration(
                              //       shape: BoxShape.circle),
                              //   child: Image.asset(
                              //     'images/Group 161.png',
                              //   ),
                              // )
                            ],
                          ),
                        ),
                        Divider(
                          color: Colors.black, // Color of the line
                        ),
                        SizedBox(height: 10),
                        if (widget.url != '') ...[
                          GestureDetector(
                            onTap: () {
                              _launchUrl(widget.url);
                            },
                            child: Text(
                              widget.url,
                              style: dairyTextStyle.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
