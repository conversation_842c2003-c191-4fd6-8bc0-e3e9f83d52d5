import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:silverleaf/api/firebase_api.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class OtpScreen extends StatefulWidget {
  final String otp;
  final List details;

  const OtpScreen({
    Key? key,
    required this.otp,
    required this.details,
  }) : super(key: key);

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> with TickerProviderStateMixin {
  final List<TextEditingController> _otpControllers = List.generate(4, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  bool _isLoading = false;
  int _resendTimer = 30;
  Timer? _timer;
  String? _selectedStudentId;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startResendTimer();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }

  void _startResendTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendTimer > 0) {
        setState(() {
          _resendTimer--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  String _getEnteredOtp() {
    return _otpControllers.map((controller) => controller.text).join();
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty && index < 3) {
      _focusNodes[index + 1].requestFocus();
    }
    
    if (_getEnteredOtp().length == 4) {
      _verifyOtp();
    }
  }

  void _onBackspace(int index) {
    if (index > 0 && _otpControllers[index].text.isEmpty) {
      _focusNodes[index - 1].requestFocus();
    }
  }

  Future<void> _verifyOtp() async {
    final enteredOtp = _getEnteredOtp();
    
    if (enteredOtp.length != 4) {
      _showErrorSnackBar('Please enter complete OTP');
      return;
    }

    if (enteredOtp != widget.otp) {
      _showErrorSnackBar('Invalid OTP. Please try again.');
      _clearOtp();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.details.length > 1) {
        _showStudentSelectionDialog();
      } else {
        await _loginStudent(widget.details[0]);
      }
    } catch (e) {
      _showErrorSnackBar('Login failed. Please try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showStudentSelectionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              title: Text('Select Student', style: AppTextStyles.titleLarge),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: widget.details.length,
                  itemBuilder: (context, index) {
                    final student = widget.details[index];
                    return RadioListTile<String>(
                      value: student['id'].toString(),
                      groupValue: _selectedStudentId,
                      onChanged: (value) {
                        setDialogState(() {
                          _selectedStudentId = value;
                        });
                      },
                      title: Text(student['name'], style: AppTextStyles.bodyMedium),
                      subtitle: Text('Class ${student['class_name']} - ${student['section_name']}', 
                                   style: AppTextStyles.bodySmall),
                      secondary: CircleAvatar(
                        backgroundImage: NetworkImage(student['profile']),
                        onBackgroundImageError: (_, __) {},
                        child: student['profile'].isEmpty 
                            ? const Icon(Icons.person) 
                            : null,
                      ),
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    setState(() {
                      _isLoading = false;
                    });
                  },
                  child: Text('Cancel', style: AppTextStyles.labelLarge.copyWith(color: AppColors.textSecondary)),
                ),
                ElevatedButton(
                  onPressed: _selectedStudentId != null
                      ? () async {
                          Navigator.pop(context);
                          final selectedStudent = widget.details.firstWhere(
                            (student) => student['id'].toString() == _selectedStudentId,
                          );
                          await _loginStudent(selectedStudent);
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: Text('Continue', style: AppTextStyles.buttonMedium),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _loginStudent(Map<String, dynamic> student) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setStringList('users', [
        student['id'].toString(),
        student['name'],
        student['class_name'],
        student['branch_name'],
        student['section_name'],
        student['section_id'].toString(),
        student['class_id'].toString(),
        student['branch_id'].toString(),
        student['profile'],
        student['blood_group_name'],
        student['father_name'],
        student['mother_name'],
        student['primary_number'].toString(),
        student['secondary_number'].toString(),
        student['academic_id'].toString(),
        student['type'].toString(),
      ]);

      await prefs.setStringList('birthday', [
        student['dob'],
        'pending',
      ]);

      final notificationManager = FirebaseApi();
      notificationManager.Auth_Users();

      Get.offAll(() => const MainBoard());
    } catch (e) {
      _showErrorSnackBar('Failed to save user data. Please try again.');
    }
  }

  void _clearOtp() {
    for (var controller in _otpControllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
  }

  void _resendOtp() {
    if (_resendTimer == 0) {
      setState(() {
        _resendTimer = 30;
      });
      _startResendTimer();
      _showSuccessSnackBar('OTP resent successfully');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: AppTextStyles.bodyMedium.copyWith(color: AppColors.textOnPrimary)),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: AppTextStyles.bodyMedium.copyWith(color: AppColors.textOnPrimary)),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              height: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom,
              child: Column(
                children: [
                  // Back Button
                  Align(
                    alignment: Alignment.centerLeft,
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: AppColors.textOnPrimary,
                      ),
                    ),
                  ),
                  
                  SizedBox(height: 4.0.hp),
                  
                  // Header Section
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: AppColors.surface,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.cardShadow,
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.sms,
                            size: 40,
                            color: AppColors.primary,
                          ),
                        ),
                        
                        SizedBox(height: 3.0.hp),
                        
                        Text(
                          'Verify OTP',
                          style: AppTextStyles.displayMedium.copyWith(
                            color: AppColors.textOnPrimary,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        
                        SizedBox(height: 1.0.hp),
                        
                        Text(
                          'Enter the 4-digit code sent to your mobile',
                          style: AppTextStyles.bodyLarge.copyWith(
                            color: AppColors.textOnPrimary.withOpacity(0.9),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  
                  SizedBox(height: 6.0.hp),
                  
                  // OTP Input Section
                  SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.cardShadow,
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // OTP Input Fields
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: List.generate(4, (index) {
                                return SizedBox(
                                  width: 60,
                                  height: 60,
                                  child: TextFormField(
                                    controller: _otpControllers[index],
                                    focusNode: _focusNodes[index],
                                    keyboardType: TextInputType.number,
                                    textAlign: TextAlign.center,
                                    maxLength: 1,
                                    style: AppTextStyles.headlineLarge.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                    decoration: InputDecoration(
                                      counterText: '',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: const BorderSide(color: AppColors.divider),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: const BorderSide(color: AppColors.divider),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: const BorderSide(color: AppColors.primary, width: 2),
                                      ),
                                      filled: true,
                                      fillColor: AppColors.surfaceVariant,
                                    ),
                                    onChanged: (value) => _onOtpChanged(value, index),
                                    onTap: () {
                                      _otpControllers[index].selection = TextSelection.fromPosition(
                                        TextPosition(offset: _otpControllers[index].text.length),
                                      );
                                    },
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                );
                              }),
                            ),
                            
                            SizedBox(height: 3.0.hp),
                            
                            // Verify Button
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _verifyOtp,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primary,
                                  foregroundColor: AppColors.textOnPrimary,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 0,
                                ),
                                child: _isLoading
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(
                                            AppColors.textOnPrimary,
                                          ),
                                        ),
                                      )
                                    : Text(
                                        'Verify OTP',
                                        style: AppTextStyles.buttonLarge,
                                      ),
                              ),
                            ),
                            
                            SizedBox(height: 2.0.hp),
                            
                            // Resend OTP
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Didn\'t receive the code? ',
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                GestureDetector(
                                  onTap: _resendOtp,
                                  child: Text(
                                    _resendTimer > 0 ? 'Resend in ${_resendTimer}s' : 'Resend',
                                    style: AppTextStyles.bodyMedium.copyWith(
                                      color: _resendTimer > 0 ? AppColors.textSecondary : AppColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
